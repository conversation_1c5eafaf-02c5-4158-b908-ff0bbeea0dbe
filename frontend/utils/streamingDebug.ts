/**
 * 流式响应调试工具
 */

interface StreamingMetrics {
  startTime: number
  endTime?: number
  chunkCount: number
  totalBytes: number
  averageChunkSize: number
  chunksPerSecond: number
  lastChunkTime: number
  chunks: Array<{
    timestamp: number
    size: number
    content: string
    type: string
  }>
}

export class StreamingDebugger {
  private metrics: StreamingMetrics
  private isEnabled: boolean

  constructor(enabled: boolean = false) {
    this.isEnabled = enabled
    this.metrics = this.resetMetrics()
  }

  private resetMetrics(): StreamingMetrics {
    return {
      startTime: Date.now(),
      chunkCount: 0,
      totalBytes: 0,
      averageChunkSize: 0,
      chunksPerSecond: 0,
      lastChunkTime: Date.now(),
      chunks: []
    }
  }

  start() {
    if (!this.isEnabled) return
    
    this.metrics = this.resetMetrics()
    console.log('🚀 流式响应调试开始', {
      startTime: new Date(this.metrics.startTime).toISOString()
    })
  }

  recordChunk(content: string, type: string = 'ai_chunk') {
    if (!this.isEnabled) return

    const now = Date.now()
    const size = new Blob([content]).size

    this.metrics.chunkCount++
    // 修复：只累积chunk大小，不累积总内容大小
    this.metrics.totalBytes += size
    this.metrics.lastChunkTime = now

    // 记录块信息
    this.metrics.chunks.push({
      timestamp: now,
      size,
      content: content.substring(0, 100), // 只保存前100个字符
      type
    })

    // 计算平均值
    this.metrics.averageChunkSize = this.metrics.totalBytes / this.metrics.chunkCount
    const elapsedSeconds = (now - this.metrics.startTime) / 1000
    this.metrics.chunksPerSecond = this.metrics.chunkCount / elapsedSeconds

    // 实时日志
    console.log(`📦 块 ${this.metrics.chunkCount}:`, {
      type,
      size: `${size} bytes`,
      elapsed: `${elapsedSeconds.toFixed(2)}s`,
      speed: `${this.metrics.chunksPerSecond.toFixed(2)} chunks/s`,
      content: content.substring(0, 50) + (content.length > 50 ? '...' : '')
    })

    // 每10个块输出一次统计
    if (this.metrics.chunkCount % 10 === 0) {
      this.logStats()
    }
  }

  // 新增：记录完整内容长度的方法
  recordContentLength(totalContent: string) {
    if (!this.isEnabled) return

    const totalSize = new Blob([totalContent]).size
    console.log(`📏 当前内容总长度: ${totalSize} bytes (${totalContent.length} 字符)`)
  }

  end() {
    if (!this.isEnabled) return

    this.metrics.endTime = Date.now()
    const totalTime = (this.metrics.endTime - this.metrics.startTime) / 1000

    console.log('✅ 流式响应调试结束', {
      totalTime: `${totalTime.toFixed(2)}s`,
      totalChunks: this.metrics.chunkCount,
      totalBytes: `${this.metrics.totalBytes} bytes`,
      averageChunkSize: `${this.metrics.averageChunkSize.toFixed(2)} bytes`,
      averageSpeed: `${this.metrics.chunksPerSecond.toFixed(2)} chunks/s`,
      throughput: `${(this.metrics.totalBytes / totalTime / 1024).toFixed(2)} KB/s`
    })

    this.generateReport()
  }

  private logStats() {
    const elapsedSeconds = (this.metrics.lastChunkTime - this.metrics.startTime) / 1000
    
    console.log('📊 流式响应统计:', {
      chunks: this.metrics.chunkCount,
      bytes: `${this.metrics.totalBytes} bytes`,
      elapsed: `${elapsedSeconds.toFixed(2)}s`,
      avgChunkSize: `${this.metrics.averageChunkSize.toFixed(2)} bytes`,
      speed: `${this.metrics.chunksPerSecond.toFixed(2)} chunks/s`
    })
  }

  private generateReport() {
    if (!this.isEnabled || this.metrics.chunks.length === 0) return

    // 分析块大小分布
    const chunkSizes = this.metrics.chunks.map(c => c.size)
    const minSize = Math.min(...chunkSizes)
    const maxSize = Math.max(...chunkSizes)
    
    // 分析时间间隔
    const intervals = []
    for (let i = 1; i < this.metrics.chunks.length; i++) {
      intervals.push(this.metrics.chunks[i].timestamp - this.metrics.chunks[i-1].timestamp)
    }
    const avgInterval = intervals.length > 0 ? intervals.reduce((a, b) => a + b, 0) / intervals.length : 0

    // 按类型统计
    const typeStats = this.metrics.chunks.reduce((acc, chunk) => {
      acc[chunk.type] = (acc[chunk.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    console.log('📈 流式响应详细报告:', {
      summary: {
        totalChunks: this.metrics.chunkCount,
        totalBytes: this.metrics.totalBytes,
        duration: `${((this.metrics.endTime! - this.metrics.startTime) / 1000).toFixed(2)}s`
      },
      chunkSizes: {
        min: `${minSize} bytes`,
        max: `${maxSize} bytes`,
        avg: `${this.metrics.averageChunkSize.toFixed(2)} bytes`
      },
      timing: {
        avgInterval: `${avgInterval.toFixed(2)}ms`,
        chunksPerSecond: this.metrics.chunksPerSecond.toFixed(2)
      },
      typeDistribution: typeStats
    })
  }

  // 获取当前指标
  getMetrics(): StreamingMetrics {
    return { ...this.metrics }
  }

  // 启用/禁用调试
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled
  }
}

// 全局调试器实例
export const streamingDebugger = new StreamingDebugger(
  // 在开发环境中启用调试
  process.env.NODE_ENV === 'development'
)

// 便捷函数
export const debugStream = {
  start: () => streamingDebugger.start(),
  chunk: (content: string, type?: string) => streamingDebugger.recordChunk(content, type),
  end: () => streamingDebugger.end(),
  enable: () => streamingDebugger.setEnabled(true),
  disable: () => streamingDebugger.setEnabled(false)
}
