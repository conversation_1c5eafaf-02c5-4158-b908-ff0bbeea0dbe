'use client'

import { useState, useRef } from 'react'
import { Button } from './ui/Button'
import { StreamingRenderer } from './ui/StreamingTextRenderer'
import { StreamingMarkdownDisplay } from './ui/StreamingMarkdownDisplay'
import { debugStream } from '../utils/streamingDebug'

export function StreamingTest() {
  const [content, setContent] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout>()

  // 模拟流式响应
  const simulateStreaming = () => {
    const mockResponse = `# AI分析报告

## 概述
这是一个模拟的AI分析报告，用于测试流式渲染功能。

## 主要发现

### 1. 技术创新
- 公司在人工智能领域投入巨大
- 研发团队规模持续扩大
- 专利申请数量显著增长

### 2. 市场表现
- 营收同比增长25%
- 市场份额稳步提升
- 客户满意度持续改善

### 3. 协同创新
- 与高校建立合作关系
- 参与行业标准制定
- 开展国际技术交流

## 详细分析

### 技术发展趋势
在当前的技术发展环境下，公司展现出了强劲的创新能力。通过持续的研发投入和人才引进，公司在核心技术领域取得了重要突破。

### 市场竞争优势
公司凭借其独特的技术优势和市场定位，在激烈的市场竞争中脱颖而出。产品质量和服务水平的不断提升，为公司赢得了良好的市场声誉。

### 未来发展机遇
随着行业政策的支持和市场需求的增长，公司面临着良好的发展机遇。通过加强技术创新和市场拓展，公司有望实现更大的发展。

## 建议

1. **加强研发投入**：继续增加研发资金投入，提升技术创新能力
2. **优化人才结构**：引进高端技术人才，完善人才培养体系
3. **拓展合作网络**：加强与产业链上下游企业的合作
4. **提升品牌影响力**：加大市场推广力度，提升品牌知名度

## 结论

综合分析表明，公司在技术创新、市场表现和协同发展等方面都表现出色，具备良好的发展前景。建议公司继续坚持创新驱动发展战略，不断提升核心竞争力。

*本报告基于公开信息分析，仅供参考。*`

    setContent('')
    setIsStreaming(true)
    debugStream.start()

    let index = 0
    const chunkSize = 50 // 每次添加50个字符

    intervalRef.current = setInterval(() => {
      if (index < mockResponse.length) {
        const chunk = mockResponse.slice(index, index + chunkSize)
        setContent(prev => prev + chunk)
        debugStream.chunk(chunk, 'mock_chunk')
        index += chunkSize
      } else {
        // 流式完成
        setIsStreaming(false)
        debugStream.end()
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    }, 100) // 每100ms添加一块
  }

  const stopStreaming = () => {
    setIsStreaming(false)
    debugStream.end()
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  const clearContent = () => {
    setContent('')
    setIsStreaming(false)
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          流式渲染测试
        </h1>
        
        <div className="flex space-x-4 mb-4">
          <Button
            onClick={simulateStreaming}
            disabled={isStreaming}
            className="bg-blue-500 hover:bg-blue-600"
          >
            {isStreaming ? '流式进行中...' : '开始模拟流式响应'}
          </Button>
          
          <Button
            onClick={stopStreaming}
            disabled={!isStreaming}
            variant="outline"
          >
            停止流式
          </Button>
          
          <Button
            onClick={clearContent}
            variant="outline"
          >
            清空内容
          </Button>
          
          <Button
            onClick={() => debugStream.enable()}
            variant="outline"
            size="sm"
          >
            启用调试
          </Button>
          
          <Button
            onClick={() => debugStream.disable()}
            variant="outline"
            size="sm"
          >
            禁用调试
          </Button>
        </div>

        <div className="text-sm text-gray-600 mb-4">
          <p>状态: {isStreaming ? '🟢 流式进行中' : '🔴 已停止'}</p>
          <p>内容长度: {content.length} 字符</p>
        </div>
      </div>

      <div className="border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">流式渲染结果</h2>
        
        <StreamingRenderer
          content={content}
          isStreaming={isStreaming}
        />
      </div>

      <div className="mt-6 text-sm text-gray-500">
        <h3 className="font-medium mb-2">使用说明:</h3>
        <ul className="list-disc list-inside space-y-1">
          <li>点击"开始模拟流式响应"开始测试</li>
          <li>观察内容是否实时更新</li>
          <li>流式完成后可以切换到Markdown渲染</li>
          <li>打开浏览器控制台查看调试信息</li>
        </ul>
      </div>
    </div>
  )
}
