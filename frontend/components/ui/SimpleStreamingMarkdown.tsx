'use client'

import { useState, useEffect, useRef, memo } from 'react'
import { MarkdownRenderer } from './MarkdownRenderer'

interface SimpleStreamingMarkdownProps {
  content: string
  className?: string
  isStreaming?: boolean
  onComplete?: () => void
}

/**
 * 简化的流式Markdown渲染器
 * 专门用于AI分析结果，无字符统计，专注于流式响应和markdown渲染
 */
export const SimpleStreamingMarkdown = memo(function SimpleStreamingMarkdown({ 
  content, 
  className = '',
  isStreaming = false,
  onComplete
}: SimpleStreamingMarkdownProps) {
  const [displayContent, setDisplayContent] = useState('')
  const [showCursor, setShowCursor] = useState(false)
  const [renderMode, setRenderMode] = useState<'text' | 'markdown'>('text')
  const containerRef = useRef<HTMLDivElement>(null)
  const lastContentRef = useRef('')
  const animationFrameRef = useRef<number>()

  // 当内容变化时更新显示内容
  useEffect(() => {
    if (content !== lastContentRef.current) {
      lastContentRef.current = content
      setDisplayContent(content)
      
      // 自动滚动到底部
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
      
      animationFrameRef.current = requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight
        }
      })
    }
  }, [content])

  // 控制光标闪烁和渲染模式
  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (isStreaming) {
      setShowCursor(true)
      setRenderMode('text') // 流式状态下强制使用文本模式
      
      interval = setInterval(() => {
        setShowCursor(prev => !prev)
      }, 500)
    } else {
      setShowCursor(false)
      
      // 流式完成后自动切换到markdown模式
      if (content.trim()) {
        setTimeout(() => {
          setRenderMode('markdown')
          onComplete?.()
        }, 500)
      }
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isStreaming, content, onComplete])

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  // Markdown渲染模式
  if (renderMode === 'markdown' && !isStreaming && displayContent.trim()) {
    return (
      <div className={className}>
        <div 
          ref={containerRef}
          className="bg-white rounded-lg border border-gray-200 overflow-hidden"
          style={{ 
            maxHeight: '600px', 
            overflowY: 'auto'
          }}
        >
          <MarkdownRenderer 
            content={displayContent} 
            className="p-6"
          />
        </div>
        
        {/* 渲染模式切换按钮 */}
        <div className="mt-4 flex justify-center space-x-3">
          <button
            onClick={() => setRenderMode('text')}
            className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            查看原始文本
          </button>
          <button
            onClick={() => setRenderMode('markdown')}
            className="px-4 py-2 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Markdown渲染
          </button>
        </div>
      </div>
    )
  }

  // 文本模式（流式状态或手动选择）
  return (
    <div className={className}>
      <div 
        ref={containerRef}
        className="bg-gray-50 border border-gray-200 rounded-lg p-4 font-mono text-sm"
        style={{ 
          maxHeight: '600px', 
          overflowY: 'auto',
          lineHeight: '1.6',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }}
      >
        {displayContent}
        {/* 流式输入光标 */}
        {isStreaming && (
          <span 
            className={`inline-block w-2 h-4 bg-blue-500 ml-1 transition-opacity duration-300 ${
              showCursor ? 'opacity-100' : 'opacity-0'
            }`}
            style={{ verticalAlign: 'text-top' }}
          />
        )}
      </div>
      
      {/* 流式状态指示器 */}
      {isStreaming && (
        <div className="mt-3 flex items-center justify-center text-sm text-gray-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>AI正在分析中，请稍候...</span>
          </div>
        </div>
      )}
      
      {/* 完成后的切换按钮 */}
      {!isStreaming && displayContent.trim() && (
        <div className="mt-4 flex justify-center space-x-3">
          <button
            onClick={() => setRenderMode('text')}
            className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            原始文本
          </button>
          <button
            onClick={() => setRenderMode('markdown')}
            className="px-4 py-2 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Markdown渲染
          </button>
        </div>
      )}
    </div>
  )
})

export default SimpleStreamingMarkdown
