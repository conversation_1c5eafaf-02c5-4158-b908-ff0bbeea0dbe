'use client'

import { useState, useEffect, useRef, memo } from 'react'
import { MarkdownRenderer } from './MarkdownRenderer'

interface StreamingMarkdownDisplayProps {
  content: string
  className?: string
  isStreaming?: boolean
  onComplete?: () => void
  autoMarkdown?: boolean
}

/**
 * 专门用于AI分析结果的流式Markdown显示组件
 * 流式状态下显示原始文本，完成后自动切换到Markdown渲染
 */
export const StreamingMarkdownDisplay = memo(function StreamingMarkdownDisplay({ 
  content, 
  className = '',
  isStreaming = false,
  onComplete,
  autoMarkdown = true
}: StreamingMarkdownDisplayProps) {
  const [displayContent, setDisplayContent] = useState('')
  const [showCursor, setShowCursor] = useState(false)
  const [renderMode, setRenderMode] = useState<'text' | 'markdown'>('text')
  const [isComplete, setIsComplete] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const lastContentRef = useRef('')
  const animationFrameRef = useRef<number>()

  // 当内容变化时更新显示内容
  useEffect(() => {
    if (content !== lastContentRef.current) {
      lastContentRef.current = content
      setDisplayContent(content)
      
      // 自动滚动到底部
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
      
      animationFrameRef.current = requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight
        }
      })
    }
  }, [content])

  // 控制光标闪烁和完成状态
  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (isStreaming) {
      setShowCursor(true)
      setIsComplete(false)
      setRenderMode('text') // 流式状态下强制使用文本模式
      
      interval = setInterval(() => {
        setShowCursor(prev => !prev)
      }, 500)
    } else {
      setShowCursor(false)
      setIsComplete(true)
      
      // 流式完成后自动切换到markdown模式
      if (autoMarkdown && content) {
        setTimeout(() => {
          setRenderMode('markdown')
          onComplete?.()
        }, 500)
      }
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isStreaming, autoMarkdown, content, onComplete])

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  // Markdown渲染模式
  if (renderMode === 'markdown' && isComplete && !isStreaming) {
    return (
      <div className={className}>
        <div 
          ref={containerRef}
          className="bg-white rounded-lg border border-gray-200 overflow-hidden"
          style={{ 
            maxHeight: '600px', 
            overflowY: 'auto'
          }}
        >
          <MarkdownRenderer 
            content={displayContent} 
            className="p-6"
          />
        </div>
        
        {/* 渲染模式切换按钮 */}
        <div className="mt-4 flex justify-center space-x-3">
          <button
            onClick={() => setRenderMode('text')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              renderMode === 'text' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            原始文本
          </button>
          <button
            onClick={() => setRenderMode('markdown')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              renderMode === 'markdown' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Markdown渲染
          </button>
        </div>
      </div>
    )
  }

  // 文本模式（流式状态或手动选择）
  return (
    <div className={className}>
      <div 
        ref={containerRef}
        className="bg-gray-50 border border-gray-200 rounded-lg p-4 font-mono text-sm"
        style={{ 
          maxHeight: '600px', 
          overflowY: 'auto',
          lineHeight: '1.6',
          whiteSpace: 'pre-wrap'
        }}
      >
        {displayContent}
        {/* 流式输入光标 */}
        {isStreaming && (
          <span 
            className={`inline-block w-2 h-4 bg-blue-500 ml-1 transition-opacity duration-300 ${
              showCursor ? 'opacity-100' : 'opacity-0'
            }`}
            style={{ verticalAlign: 'text-top' }}
          />
        )}
      </div>
      
      {/* 流式状态指示器 */}
      {isStreaming && (
        <div className="mt-3 flex items-center justify-center text-sm text-gray-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>AI正在分析中，请稍候...</span>
          </div>
        </div>
      )}
      
      {/* 完成后的切换按钮 */}
      {isComplete && !isStreaming && autoMarkdown && (
        <div className="mt-4 flex justify-center space-x-3">
          <button
            onClick={() => setRenderMode('text')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              renderMode === 'text' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            原始文本
          </button>
          <button
            onClick={() => setRenderMode('markdown')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              renderMode === 'markdown' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Markdown渲染
          </button>
        </div>
      )}
    </div>
  )
})

// 简化的流式渲染器接口，保持向后兼容
interface SimpleStreamingRendererProps {
  content: string
  isStreaming?: boolean
  className?: string
  onComplete?: () => void
}

export function SimpleStreamingRenderer({ 
  content, 
  isStreaming = false, 
  className = '',
  onComplete
}: SimpleStreamingRendererProps) {
  return (
    <StreamingMarkdownDisplay
      content={content}
      isStreaming={isStreaming}
      className={className}
      onComplete={onComplete}
      autoMarkdown={true}
    />
  )
}
