'use client'

import { useState, useEffect, useRef } from 'react'
import { StreamingMarkdownRenderer } from './StreamingMarkdownRenderer'
import { Button } from './Button'
import { Badge } from './Badge'
import { Clock, Bot, FileText, Download, Trash2 } from 'lucide-react'

interface AnalysisHistoryItem {
  id: string
  content: string
  timestamp: Date
  title?: string
  prompt?: string
  keywords?: string[]
  companies?: string[]
}

interface AIAnalysisRendererProps {
  content: string
  isStreaming?: boolean
  className?: string
  onComplete?: () => void
  showHistory?: boolean
  historyItems?: AnalysisHistoryItem[]
  onSaveHistory?: (item: AnalysisHistoryItem) => void
  onDeleteHistory?: (id: string) => void
  onExportHistory?: (item: AnalysisHistoryItem) => void
  currentPrompt?: string
  currentKeywords?: string[]
  currentCompanies?: string[]
}

export function AIAnalysisRenderer({
  content,
  isStreaming = false,
  className = '',
  onComplete,
  showHistory = true,
  historyItems = [],
  onSaveHistory,
  onDeleteHistory,
  onExportHistory,
  currentPrompt,
  currentKeywords = [],
  currentCompanies = []
}: AIAnalysisRendererProps) {
  const [selectedHistoryId, setSelectedHistoryId] = useState<string | null>(null)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true)
  const hasAutoSaved = useRef(false)

  // 自动保存当前分析结果
  useEffect(() => {
    if (!isStreaming && content && autoSaveEnabled && !hasAutoSaved.current && onSaveHistory) {
      const historyItem: AnalysisHistoryItem = {
        id: `analysis_${Date.now()}`,
        content,
        timestamp: new Date(),
        title: `分析结果 - ${new Date().toLocaleString()}`,
        prompt: currentPrompt,
        keywords: currentKeywords,
        companies: currentCompanies
      }
      
      onSaveHistory(historyItem)
      hasAutoSaved.current = true
    }
  }, [isStreaming, content, autoSaveEnabled, onSaveHistory, currentPrompt, currentKeywords, currentCompanies])

  // 重置自动保存标志
  useEffect(() => {
    if (isStreaming) {
      hasAutoSaved.current = false
    }
  }, [isStreaming])

  // 获取显示内容
  const selectedItem = selectedHistoryId 
    ? historyItems.find(item => item.id === selectedHistoryId)
    : null

  const displayContent = selectedItem ? selectedItem.content : content
  const isShowingHistory = selectedHistoryId !== null
  const isCurrentStreaming = isStreaming && !isShowingHistory

  const handleHistorySelect = (id: string | null) => {
    setSelectedHistoryId(id)
  }

  const handleExport = (item: AnalysisHistoryItem) => {
    if (onExportHistory) {
      onExportHistory(item)
    } else {
      // 默认导出为文本文件
      const blob = new Blob([item.content], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `AI分析结果_${item.timestamp.toISOString().slice(0, 19).replace(/:/g, '-')}.md`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  const handleDelete = (id: string) => {
    if (onDeleteHistory) {
      onDeleteHistory(id)
    }
    if (selectedHistoryId === id) {
      setSelectedHistoryId(null)
    }
  }

  return (
    <div className={className}>
      {/* 历史记录面板 */}
      {showHistory && historyItems.length > 0 && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-gray-900 flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              分析历史 ({historyItems.length})
            </h4>
            <div className="flex items-center space-x-2">
              <label className="flex items-center text-xs text-gray-600">
                <input
                  type="checkbox"
                  checked={autoSaveEnabled}
                  onChange={(e) => setAutoSaveEnabled(e.target.checked)}
                  className="mr-1"
                />
                自动保存
              </label>
            </div>
          </div>
          
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {/* 当前分析 */}
            <button
              onClick={() => handleHistorySelect(null)}
              className={`w-full text-left p-3 rounded border transition-colors ${
                !isShowingHistory 
                  ? 'bg-blue-50 border-blue-200 text-blue-900' 
                  : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Bot className="w-4 h-4" />
                  <span className="font-medium">当前分析</span>
                  {isStreaming && (
                    <Badge variant="success" size="sm">进行中</Badge>
                  )}
                </div>
                <span className="text-xs text-gray-500">
                  {content.length} 字符
                </span>
              </div>
            </button>

            {/* 历史记录列表 */}
            {historyItems.map((item) => (
              <div
                key={item.id}
                className={`p-3 rounded border transition-colors ${
                  selectedHistoryId === item.id 
                    ? 'bg-blue-50 border-blue-200' 
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => handleHistorySelect(item.id)}
                    className="flex-1 text-left"
                  >
                    <div className="flex items-center space-x-2 mb-1">
                      <FileText className="w-4 h-4 text-gray-500" />
                      <span className="font-medium text-sm text-gray-900">
                        {item.title || '未命名分析'}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500">
                      {item.timestamp.toLocaleString()} • {item.content.length} 字符
                    </div>
                    {item.keywords && item.keywords.length > 0 && (
                      <div className="mt-1 flex flex-wrap gap-1">
                        {item.keywords.slice(0, 3).map((keyword, idx) => (
                          <Badge key={idx} variant="outline" size="sm">
                            {keyword}
                          </Badge>
                        ))}
                        {item.keywords.length > 3 && (
                          <Badge variant="outline" size="sm">
                            +{item.keywords.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}
                  </button>
                  
                  <div className="flex items-center space-x-1 ml-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleExport(item)}
                      title="导出"
                    >
                      <Download className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(item.id)}
                      title="删除"
                    >
                      <Trash2 className="w-3 h-3 text-red-500" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 主要内容渲染器 */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <Bot className="w-5 h-5 mr-2 text-blue-600" />
              {isShowingHistory ? selectedItem?.title || '历史分析' : 'AI分析结果'}
            </h3>
            
            {isShowingHistory && selectedItem && (
              <div className="flex items-center space-x-2">
                <Badge variant="outline" size="sm">
                  {selectedItem.timestamp.toLocaleString()}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport(selectedItem)}
                >
                  <Download className="w-4 h-4 mr-1" />
                  导出
                </Button>
              </div>
            )}
          </div>
          
          {isShowingHistory && selectedItem?.prompt && (
            <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-600">
              <strong>分析要求：</strong> {selectedItem.prompt}
            </div>
          )}
        </div>
        
        <div className="p-4">
          <StreamingMarkdownRenderer
            content={displayContent}
            isStreaming={isCurrentStreaming}
            onComplete={onComplete}
            autoScroll={!isShowingHistory}
            renderMode="auto"
          />
        </div>
      </div>
    </div>
  )
}
