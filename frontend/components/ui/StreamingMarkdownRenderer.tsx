'use client'

import { useState, useEffect, useRef, memo } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import 'highlight.js/styles/github.css'
import { debugStream } from '../../utils/streamingDebug'

interface StreamingMarkdownRendererProps {
  content: string
  className?: string
  isStreaming?: boolean
  onComplete?: () => void
  renderMode?: 'auto' | 'text' | 'markdown'
  autoScroll?: boolean
}

// 使用memo优化重渲染
export const StreamingMarkdownRenderer = memo(function StreamingMarkdownRenderer({
  content,
  className = '',
  isStreaming = false,
  onComplete,
  renderMode = 'auto',
  autoScroll = true
}: StreamingMarkdownRendererProps) {
  const [displayContent, setDisplayContent] = useState('')
  const [showCursor, setShowCursor] = useState(false)
  const [currentRenderMode, setCurrentRenderMode] = useState<'text' | 'markdown'>('text')
  const [isComplete, setIsComplete] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const lastContentRef = useRef('')
  const animationFrameRef = useRef<number>()

  // 当内容变化时更新显示内容
  useEffect(() => {
    if (content !== lastContentRef.current) {
      const newChunk = content.slice(lastContentRef.current.length)
      if (newChunk) {
        debugStream.chunk(newChunk, 'ai_markdown')
      }

      lastContentRef.current = content
      setDisplayContent(content)

      // 自动滚动优化
      if (autoScroll) {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current)
        }

        animationFrameRef.current = requestAnimationFrame(() => {
          if (containerRef.current) {
            containerRef.current.scrollTop = containerRef.current.scrollHeight
          }
        })
      }
    }
  }, [content, autoScroll])

  // 控制光标闪烁和渲染模式
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isStreaming) {
      setShowCursor(true)
      setIsComplete(false)
      // 流式状态下使用文本模式
      if (renderMode === 'auto') {
        setCurrentRenderMode('text')
      }

      interval = setInterval(() => {
        setShowCursor(prev => !prev)
      }, 500)
    } else {
      setShowCursor(false)
      setIsComplete(true)

      // 流式完成后自动切换到markdown模式
      if (renderMode === 'auto') {
        setTimeout(() => {
          setCurrentRenderMode('markdown')
          onComplete?.()
        }, 300)
      }
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isStreaming, renderMode, onComplete])

  // 手动设置渲染模式
  useEffect(() => {
    if (renderMode === 'text') {
      setCurrentRenderMode('text')
    } else if (renderMode === 'markdown') {
      setCurrentRenderMode('markdown')
    }
  }, [renderMode])

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  return (
    <div 
      ref={containerRef}
      className={`prose prose-sm max-w-none ${className}`}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          // 自定义标题样式
          h1: ({ children }) => (
            <h1 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b border-gray-200">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-lg font-semibold text-gray-900 mb-3 mt-6">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-base font-medium text-gray-900 mb-2 mt-4">
              {children}
            </h3>
          ),
          h4: ({ children }) => (
            <h4 className="text-sm font-medium text-gray-900 mb-2 mt-3">
              {children}
            </h4>
          ),
          
          // 自定义段落样式
          p: ({ children }) => (
            <p className="text-gray-700 mb-3 leading-relaxed">
              {children}
            </p>
          ),
          
          // 自定义列表样式
          ul: ({ children }) => (
            <ul className="list-disc list-inside mb-3 space-y-1 text-gray-700">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside mb-3 space-y-1 text-gray-700">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="text-gray-700">
              {children}
            </li>
          ),
          
          // 自定义代码样式
          code: ({ inline, children, ...props }) => {
            if (inline) {
              return (
                <code className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-xs font-mono" {...props}>
                  {children}
                </code>
              )
            }
            return (
              <code className="block bg-gray-100 text-gray-800 p-3 rounded text-xs font-mono overflow-x-auto" {...props}>
                {children}
              </code>
            )
          },
          
          // 自定义代码块样式
          pre: ({ children }) => (
            <pre className="bg-gray-100 p-3 rounded mb-3 overflow-x-auto">
              {children}
            </pre>
          ),
          
          // 自定义引用样式
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-blue-500 pl-4 py-2 mb-3 bg-blue-50 text-gray-700 italic">
              {children}
            </blockquote>
          ),
          
          // 自定义表格样式
          table: ({ children }) => (
            <div className="overflow-x-auto mb-3">
              <table className="min-w-full border border-gray-300 rounded">
                {children}
              </table>
            </div>
          ),
          thead: ({ children }) => (
            <thead className="bg-gray-50">
              {children}
            </thead>
          ),
          tbody: ({ children }) => (
            <tbody className="bg-white">
              {children}
            </tbody>
          ),
          tr: ({ children }) => (
            <tr className="border-b border-gray-200">
              {children}
            </tr>
          ),
          th: ({ children }) => (
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-4 py-2 text-sm text-gray-900">
              {children}
            </td>
          ),
          
          // 自定义链接样式
          a: ({ children, href }) => (
            <a 
              href={href} 
              className="text-blue-600 hover:text-blue-800 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              {children}
            </a>
          ),
          
          // 自定义强调样式
          strong: ({ children }) => (
            <strong className="font-semibold text-gray-900">
              {children}
            </strong>
          ),
          em: ({ children }) => (
            <em className="italic text-gray-700">
              {children}
            </em>
          ),
        }}
      >
        {displayContent}
      </ReactMarkdown>
      
      {/* 流式输入光标 */}
      {isStreaming && (
        <span 
          className={`inline-block w-2 h-4 bg-blue-500 ml-1 transition-opacity duration-300 ${
            showCursor ? 'opacity-100' : 'opacity-0'
          }`}
        />
      )}
    </div>
  )
})
